# SPDX-FileCopyrightText: Copyright (c) 2021 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: BSD-3-Clause
# 
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
# 1. Redistributions of source code must retain the above copyright notice, this
# list of conditions and the following disclaimer.
#
# 2. Redistributions in binary form must reproduce the above copyright notice,
# this list of conditions and the following disclaimer in the documentation
# and/or other materials provided with the distribution.
#
# 3. Neither the name of the copyright holder nor the names of its
# contributors may be used to endorse or promote products derived from
# this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
# FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
# DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
# SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
# CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
# OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
# Copyright (c) 2021 ETH Zurich, Nikita Rudin

import numpy as np
from numpy.random import choice
from scipy import interpolate

from isaacgym import terrain_utils
from legged_gym.envs.base.legged_robot_config import LeggedRobotCfg

import trimesh
import os
import numpy as np

class Terrain:
    def __init__(self, cfg: LeggedRobotCfg.terrain, num_robots) -> None:

        self.cfg = cfg
        self.num_robots = num_robots
        self.type = cfg.mesh_type
        if self.type in ["none", 'plane']:
            return
        self.env_length = cfg.terrain_length
        self.env_width = cfg.terrain_width
        self.proportions = [np.sum(cfg.terrain_proportions[:i+1]) for i in range(len(cfg.terrain_proportions))]

        self.cfg.num_sub_terrains = cfg.num_rows * cfg.num_cols
        self.env_origins = np.zeros((cfg.num_rows, cfg.num_cols, 3))

        self.width_per_env_pixels = int(self.env_width / cfg.horizontal_scale)
        self.length_per_env_pixels = int(self.env_length / cfg.horizontal_scale)

        self.border = int(cfg.border_size/self.cfg.horizontal_scale)
        self.tot_cols = int(cfg.num_cols * self.width_per_env_pixels) + 2 * self.border
        self.tot_rows = int(cfg.num_rows * self.length_per_env_pixels) + 2 * self.border

        self.height_field_raw = np.zeros((self.tot_rows , self.tot_cols), dtype=np.int16)

        # add code
        # 收集所有圆角楼梯mesh的列表
        self.collected_stair_meshes = []

        # 存储每个地形块的速度倾向信息 (num_rows, num_cols)
        # 正值表示倾向前进，负值表示倾向后退，0表示无倾向
        self.terrain_velocity_bias = np.zeros((cfg.num_rows, cfg.num_cols), dtype=np.float32)

        
        if cfg.curriculum:
            self.curiculum()
        elif cfg.selected:
            self.selected_terrain()
        else:    
            self.randomized_terrain()   
        
        self.heightsamples = self.height_field_raw
        if self.type=="trimesh":
            self.vertices, self.triangles = terrain_utils.convert_heightfield_to_trimesh(   self.height_field_raw,
                                                                                            self.cfg.horizontal_scale,
                                                                                            self.cfg.vertical_scale,
                                                                                            self.cfg.slope_treshold)
    
            base_mesh = trimesh.Trimesh(vertices=self.vertices, faces=self.triangles)
            additional_meshes = [base_mesh]

            # print("stair_info nums is: ", len(self.collected_stair_meshes))
            # print("base_mesh: ", base_mesh.centroid)
            
            # 合并所有收集到的圆角楼梯mesh
            if len(self.collected_stair_meshes) > 0:
                additional_meshes.extend(self.collected_stair_meshes)
                # print("self.collected_stair_meshes[384]: ", self.collected_stair_meshes[384].centroid)
                # print("self.collected_stair_meshes[385]: ", self.collected_stair_meshes[385].centroid)
                # print("self.collected_stair_meshes[386]: ", self.collected_stair_meshes[386].centroid)
                # print("self.collected_stair_meshes[387]: ", self.collected_stair_meshes[387].centroid)
                # print("additional_meshes nums is: ", len(additional_meshes))
                # 合并所有mesh
                combined_mesh = trimesh.util.concatenate(additional_meshes)

                # 更新vertices和triangles
                self.vertices = np.array(combined_mesh.vertices, dtype=np.float32)
                self.triangles = np.array(combined_mesh.faces, dtype=np.uint32)
    
    def randomized_terrain(self):
        for k in range(self.cfg.num_sub_terrains):
            # Env coordinates in the world
            (i, j) = np.unravel_index(k, (self.cfg.num_rows, self.cfg.num_cols))

            choice = np.random.uniform(0, 1)
            difficulty = np.random.choice([0.5, 0.75, 0.9])
            # terrain = self.make_terrain(choice, difficulty)
            terrain = self.make_terrain(choice, difficulty, i, j)
            self.add_terrain_to_map(terrain, i, j)
        
    def curiculum(self):
        for j in range(self.cfg.num_cols):
            for i in range(self.cfg.num_rows):
                difficulty = i / self.cfg.num_rows
                choice = j / self.cfg.num_cols + 0.001

                # terrain = self.make_terrain(choice, difficulty)
                terrain = self.make_terrain(choice, difficulty, i, j)
                self.add_terrain_to_map(terrain, i, j)

    def selected_terrain(self):
        terrain_type = self.cfg.terrain_kwargs.pop('type')
        for k in range(self.cfg.num_sub_terrains):
            # Env coordinates in the world
            (i, j) = np.unravel_index(k, (self.cfg.num_rows, self.cfg.num_cols))

            terrain = terrain_utils.SubTerrain("terrain",
                              width=self.width_per_env_pixels,
                              length=self.width_per_env_pixels,
                              vertical_scale=self.vertical_scale,
                              horizontal_scale=self.horizontal_scale)

            eval(terrain_type)(terrain, **self.cfg.terrain_kwargs.terrain_kwargs)
            self.add_terrain_to_map(terrain, i, j)
    
    # def make_terrain(self, choice, difficulty):
    def make_terrain(self, choice, difficulty, env_row_idx, env_col_idx):
        terrain = terrain_utils.SubTerrain(   "terrain",
                                width=self.width_per_env_pixels,
                                length=self.width_per_env_pixels,
                                vertical_scale=self.cfg.vertical_scale,
                                horizontal_scale=self.cfg.horizontal_scale)
        slope = difficulty * 0.4
        # slope = difficulty * 0.8
        amplitude = 0.01 + 0.07 * difficulty
        step_height = 0.05 + 0.18 * difficulty
        # # new add
        # step_width = 0.5 - 0.4 * difficulty

        # # test for tmp
        # step_height = 0.068
        discrete_obstacles_height = 0.05 + difficulty * 0.1
        stepping_stones_size = 1.5 * (1.05 - difficulty)
        stone_distance = 0.05 if difficulty==0 else 0.1
        gap_size = 1. * difficulty
        pit_depth = 1. * difficulty
        if choice < self.proportions[0]:  # 平地地形
            # 创建平地地形 - 确保完全平坦
            flat_terrain(terrain)
        elif choice < self.proportions[1]:  # 斜坡地形
            if choice < (self.proportions[1] + self.proportions[0]) / 2:
                slope *= -1
            terrain_utils.pyramid_sloped_terrain(terrain, slope=slope, platform_size=3.)
        elif choice < self.proportions[2]: # 随机起伏斜坡
            terrain_utils.pyramid_sloped_terrain(terrain, slope=slope, platform_size=3.)
            terrain_utils.random_uniform_terrain(terrain, min_height=-amplitude, max_height=amplitude, step=0.005, downsampled_scale=0.2)
        elif choice < self.proportions[4]: # 阶梯地形
            if choice<self.proportions[3]:
                step_height *= -1

            # 根据机器人预期速度方向决定是否添加圆角
            # 为每个地形块预先分配一个速度方向倾向
            # 使用地形块的位置作为随机种子，确保一致性
            np.random.seed(env_row_idx * self.cfg.num_cols + env_col_idx)
            expected_forward_velocity = np.random.uniform(-1.0, 1.0)

            # 存储这个地形块的速度倾向
            self.terrain_velocity_bias[env_row_idx, env_col_idx] = expected_forward_velocity

            # 如果预期速度为前进（正值），使用圆角楼梯；如果为后退（负值），使用普通楼梯
            if expected_forward_velocity > 0:
                # 前进速度：使用圆角楼梯
                self.pyramid_stairs_terrain_with_3quarter_round_edges_intuitive(
                    terrain,
                    step_width=0.31,
                    step_height=step_height,
                    platform_size=2.0,
                    corner_radius=0.04  # 可以根据需要调整圆角半径
                )
            else:
                # 后退速度：使用普通楼梯，不添加圆角
                terrain_utils.pyramid_stairs_terrain(terrain, step_width=0.31, step_height=step_height, platform_size=2.0)

            # 恢复随机种子状态
            np.random.seed()

        elif choice < self.proportions[5]: # 随机分布障碍物
            num_rectangles = 20
            rectangle_min_size = 1.
            rectangle_max_size = 2.
            terrain_utils.discrete_obstacles_terrain(terrain, discrete_obstacles_height, rectangle_min_size, rectangle_max_size, num_rectangles, platform_size=3.)
        elif choice < self.proportions[6]: # 随机分布石块
            terrain_utils.stepping_stones_terrain(terrain, stone_size=stepping_stones_size, stone_distance=stone_distance, max_height=0., platform_size=4.)
        elif choice < self.proportions[7]: # 随机分布沟壑
            gap_terrain(terrain, gap_size=gap_size, platform_size=3.)
        else:
            pit_terrain(terrain, depth=pit_depth, platform_size=4.)

        return terrain

    def add_terrain_to_map(self, terrain, row, col):
        i = row
        j = col
        # map coordinate system
        start_x = self.border + i * self.length_per_env_pixels
        end_x = self.border + (i + 1) * self.length_per_env_pixels
        start_y = self.border + j * self.width_per_env_pixels
        end_y = self.border + (j + 1) * self.width_per_env_pixels
        self.height_field_raw[start_x: end_x, start_y:end_y] = terrain.height_field_raw

        env_origin_x = (i + 0.5) * self.env_length
        env_origin_y = (j + 0.5) * self.env_width
        x1 = int((self.env_length/2. - 1) / terrain.horizontal_scale)
        x2 = int((self.env_length/2. + 1) / terrain.horizontal_scale)
        y1 = int((self.env_width/2. - 1) / terrain.horizontal_scale)
        y2 = int((self.env_width/2. + 1) / terrain.horizontal_scale)
        env_origin_z = np.max(terrain.height_field_raw[x1:x2, y1:y2])*terrain.vertical_scale
        self.env_origins[i, j] = [env_origin_x, env_origin_y, env_origin_z]

        # new add
        # 处理圆角楼梯的全局坐标转换
        if hasattr(terrain, 'stair_info') and terrain.stair_info is not None:
            # 计算当前地形块在整个地形图中的起始位置
            start_x_world = (self.border + i * self.length_per_env_pixels) * self.cfg.horizontal_scale
            start_y_world = (self.border + j * self.width_per_env_pixels) * self.cfg.horizontal_scale
            
            # 地形块中心在整个地形图坐标系中的位置
            global_offset_x = start_x_world + self.env_length / 2
            global_offset_y = start_y_world + self.env_width / 2

            global_offset_z = 0
            # print("start_x_world is: ", start_x_world)
            # print("start_y_world is: ", start_y_world)
            # print("global_offset_x is: ", global_offset_x)
            # print("global_offset_y is: ", global_offset_y)
            # # print("global_offset_z is: ", global_offset_z)

            # print("self.env_length is: ", self.env_length)
            # print("self.env_width is: ", self.env_width)
            # global_offset_x is:  4.0
            # global_offset_y is:  4.0
            # self.env_length is:  8.0
            # self.env_width is:  8.0

            
            # 创建带全局偏移的圆角mesh
            rounded_meshes = self._create_rounded_meshes_with_offset(
                terrain.stair_info, global_offset_x, global_offset_y, global_offset_z
            )            
            # 添加到收集列表
            self.collected_stair_meshes.extend(rounded_meshes)


    def pyramid_stairs_terrain_with_3quarter_round_edges_intuitive(self, terrain, step_width, step_height, platform_size=1., corner_radius=None):
        """先创建基础楼梯，再添加3/4圆角的直观方法
        
        Args:
            terrain: 地形对象
            step_width: 每个台阶的宽度(米)
            step_height: 每个台阶的高度(米)
            platform_size: 平台尺寸(米)
            corner_radius: 圆角半径(米)
        """
        # 创建基础楼梯高度场
        terrain_utils.pyramid_stairs_terrain(terrain, step_width=step_width, step_height=step_height, platform_size=platform_size)
        
        # 分析楼梯结构，获取每一级台阶的信息
        stair_info = self._analyze_stair_structure(terrain, step_width, step_height, platform_size)
        # print(f"stair_info nums is: {len(stair_info)}")
        

        if corner_radius is None:
            corner_radius = abs(step_height) * 0.5

        # new add
        # 存储楼梯信息到terrain对象，稍后在add_terrain_to_map中处理全局坐标转换
        terrain.stair_info = {
            'levels': stair_info,
            'corner_radius': corner_radius,
            'step_width': step_width,
            'step_height': step_height,
            'platform_size': platform_size
        }
        
        return terrain

    def _analyze_stair_structure(self, terrain, step_width, step_height, platform_size):
        """分析楼梯结构，返回每一级台阶的信息"""
        width = terrain.width
        length = terrain.length
        horizontal_scale = terrain.horizontal_scale
        vertical_scale = terrain.vertical_scale
        # print("width is: ", width)
        # print("length is: ", length)
        # print("step_width: ", step_width)
        # print("step_height: ", step_height)

        # print("horizontal_scale is: ", horizontal_scale)
        # print("vertical_scale is: ", vertical_scale)
        # # width is:  80
        # # length is:  80
        # # step_width: 0.2
        # # step_height: -0.05
        # # horizontal_scale is:  0.1
        # # vertical_scale is:  0.005

        # print("step_width is: ", step_width)
        # print("step_width / horizontal_scale is: ", step_width / horizontal_scale)
        # print("int(step_width / horizontal_scale) is: ", int(step_width / horizontal_scale))
        step_width_grid = int(step_width / horizontal_scale)
        step_height_grid = int(step_height / vertical_scale)
        platform_size_grid = int(platform_size / horizontal_scale)
        # print("step_width_grid is: ", step_width_grid)
        # print("step_height_grid is: ", step_height_grid)
        # print("platform_size_grid is: ", platform_size_grid)
        # # step_width_grid is:  2
        # # step_height_grid is:  -10
        # # platform_size_grid is:  30
        
        center_x = length // 2
        center_y = width // 2
        offset = platform_size_grid // 2
        
        is_ascending = step_height > 0
        num_steps = (min(center_x, center_y) - offset) // step_width_grid
        # print("num_steps is:", num_steps)
        stair_levels = []
        
        for step in range(num_steps):
            if is_ascending:
                height = step_height * (num_steps - step)
                inner_distance = platform_size_grid + step * step_width_grid * 2
                position = platform_size_grid // 2 + step * step_width_grid
            else:
                # height = step_height * (step + 1)
                height = step_height * step
                # 计算当前级别的信息
                inner_distance = platform_size_grid + (num_steps - step - 1) * step_width_grid * 2
                # position = platform_size_grid // 2 + (num_steps - step) * step_width_grid - step_width_grid / 2
                position = platform_size_grid // 2 + (num_steps - step - 1) * step_width_grid

            
            # 转换为实际坐标
            level_info = {
                'step_index': step,
                'inner_size': round(inner_distance * horizontal_scale, 4),
                'position': round(position * horizontal_scale, 4),
                'height': round(height, 4),
                'center_x': 0,  # 相对于地形块中心，设为0
                'center_y': 0,  # 相对于地形块中心，设为0
                'step_width': step_width,
            }
            
            stair_levels.append(level_info)
        
        return stair_levels
    
    def _create_rounded_meshes_with_offset(self, stair_info, offset_x, offset_y, offset_z):
        """创建带全局偏移的圆角mesh"""
        meshes = []
        
        # 创建圆角装饰
        for level in stair_info['levels']:
            edge_cylinders = self._create_edge_cylinders_for_level(level, stair_info['corner_radius'], stair_info['platform_size'])
            meshes.extend(edge_cylinders)


        #     # 为当前级别添加角落圆角
        #     corner_spheres = self._create_corner_spheres_for_level(stair_level, corner_radius)
        #     rounded_meshes.extend(corner_spheres)
        # # print("rounded_meshes nums is: ", len(rounded_meshes))
        # # 152 = 19 * 4 * 2
        # print("meshes nums is: ", len(meshes))
        
        # # 应用全局偏移
        # for mesh in meshes:
        #     print("before offset: ", mesh.centroid)
        #     mesh.apply_translation([offset_x, offset_y, offset_z])
        #     print("after offset: ", mesh.centroid)

        # 应用全局偏移
        for i in range(len(meshes)):
            meshes[i].apply_translation([offset_x, offset_y, offset_z])
        
        return meshes
    
    def _create_edge_cylinders_for_level(self, level_info, corner_radius, platform_size):
        """为指定级别创建边缘圆柱体"""
        cylinders = []
        inner_size = level_info['inner_size']
        step_width = level_info['step_width']
        # print("step_width is: ", step_width)
        # print("inner_size is: ", inner_size)
        position = level_info['position']
        height = level_info['height']
        center_x = level_info['center_x']
        center_y = level_info['center_y']
        
        if height <= 0:
            # 四个边的位置和方向
            edges = [
                {'pos': (position, 0), 'length': inner_size, 'rotation': (np.pi/2, 0, np.pi/2)},  # 右边
                {'pos': (-position - 0.1, 0), 'length': inner_size, 'rotation': (np.pi/2, 0, -np.pi/2)}, # 左边
                {'pos': (0, position), 'length': inner_size, 'rotation': (0, np.pi/2, 0)},         # 前边
                {'pos': (0, -position - 0.1), 'length': inner_size, 'rotation': (0, np.pi/2, np.pi)},   # 后边
            ]
        else:
            edges = [
                {'pos': (position - 0.1, 0), 'length': inner_size, 'rotation': (np.pi/2, 0, np.pi/2)},  # 右边
                {'pos': (-position, 0), 'length': inner_size, 'rotation': (np.pi/2, 0, -np.pi/2)}, # 左边
                {'pos': (0, position - 0.1), 'length': inner_size, 'rotation': (0, np.pi/2, 0)},         # 前边
                {'pos': (0, -position), 'length': inner_size, 'rotation': (0, np.pi/2, np.pi)},   # 后边
            ]
        
        for edge in edges:
            # 创建3/4圆柱体
            cylinder = self._create_3quarter_cylinder(
                radius=corner_radius,
                height=edge['length'],
                sections=32
            )
            
            # 应用旋转 - 修改为对应的轴
            if edge['rotation'][2] != 0:  # Z轴旋转 
                cylinder.apply_transform(trimesh.transformations.rotation_matrix(edge['rotation'][2], [0, 0, 1]))
            if edge['rotation'][1] != 0:  # Y轴旋转
                cylinder.apply_transform(trimesh.transformations.rotation_matrix(edge['rotation'][1], [0, 1, 0]))
            if edge['rotation'][0] != 0:  # X轴旋转
                cylinder.apply_transform(trimesh.transformations.rotation_matrix(edge['rotation'][0], [1, 0, 0]))
            
            # 移动到正确位置
            cylinder.apply_translation([
                center_x + edge['pos'][0],
                center_y + edge['pos'][1],
                # height - corner_radius
                height
            ])
            
            cylinders.append(cylinder)
        
        return cylinders
    
    def _create_3quarter_cylinder(self, radius, height, sections=32):
        """创建3/4圆柱体"""
        
        # 创建完整圆柱体
        cylinder = trimesh.creation.cylinder(radius=radius, height=height, sections=sections)
        
        # 创建裁剪盒，只保留3/4部分
        # 移除1/4部分（从-45度到45度的扇形）
        clip_box = trimesh.creation.box(
            [radius * 2, radius * 2, height * 2], 
            center=[radius * 0.7, 0, 0]  # 偏移裁剪盒以移除1/4
        )
        
        # 如果有布尔操作支持，使用difference操作
        try:
            cylinder = cylinder.difference(clip_box)
        except:
            # 如果没有布尔操作支持，返回完整圆柱体
            pass
        
        return cylinder

    
def gap_terrain(terrain, gap_size, platform_size=1.):
    gap_size = int(gap_size / terrain.horizontal_scale)
    platform_size = int(platform_size / terrain.horizontal_scale)

    center_x = terrain.length // 2
    center_y = terrain.width // 2
    x1 = (terrain.length - platform_size) // 2
    x2 = x1 + gap_size
    y1 = (terrain.width - platform_size) // 2
    y2 = y1 + gap_size
   
    terrain.height_field_raw[center_x-x2 : center_x + x2, center_y-y2 : center_y + y2] = -1000
    terrain.height_field_raw[center_x-x1 : center_x + x1, center_y-y1 : center_y + y1] = 0

def pit_terrain(terrain, depth, platform_size=1.):
    depth = int(depth / terrain.vertical_scale)
    platform_size = int(platform_size / terrain.horizontal_scale / 2)
    x1 = terrain.length // 2 - platform_size
    x2 = terrain.length // 2 + platform_size
    y1 = terrain.width // 2 - platform_size
    y2 = terrain.width // 2 + platform_size
    terrain.height_field_raw[x1:x2, y1:y2] = -depth

def flat_terrain(terrain):
    """
    生成完全平坦的地形
    
    Args:
        terrain: SubTerrain对象
    """
    # 将整个高度场设置为0，创建完全平坦的表面
    terrain.height_field_raw[:, :] = 0
