#!/usr/bin/env python3

"""
测试地形速度倾向功能的脚本
"""

import numpy as np
import sys
import os

# 添加项目路径
sys.path.append('/root/yujun.zhang/code/VitaHIMloco')

from legged_gym.utils.terrain import Terrain
from legged_gym.envs.base.legged_robot_config import LeggedRobotCfg

def test_terrain_velocity_bias():
    """测试地形速度倾向功能"""
    
    # 创建一个简单的地形配置
    cfg = LeggedRobotCfg()
    cfg.terrain.mesh_type = 'trimesh'
    cfg.terrain.num_rows = 5
    cfg.terrain.num_cols = 5
    cfg.terrain.terrain_length = 8.0
    cfg.terrain.terrain_width = 8.0
    cfg.terrain.horizontal_scale = 0.1
    cfg.terrain.vertical_scale = 0.005
    cfg.terrain.border_size = 1.0
    cfg.terrain.curriculum = False
    cfg.terrain.selected = False
    
    # 设置地形比例，让楼梯地形占主要部分
    cfg.terrain.terrain_proportions = [0.1, 0.1, 0.1, 0.3, 0.3, 0.05, 0.05, 0.0, 0.0]
    
    num_robots = 25  # 5x5 = 25个环境
    
    print("创建地形...")
    terrain = Terrain(cfg.terrain, num_robots)
    
    print(f"地形速度倾向矩阵形状: {terrain.terrain_velocity_bias.shape}")
    print("地形速度倾向矩阵:")
    print(terrain.terrain_velocity_bias)
    
    # 统计圆角楼梯和普通楼梯的数量
    forward_bias_count = np.sum(terrain.terrain_velocity_bias > 0)
    backward_bias_count = np.sum(terrain.terrain_velocity_bias < 0)
    neutral_count = np.sum(terrain.terrain_velocity_bias == 0)
    
    print(f"\n统计结果:")
    print(f"前进倾向地形块数量 (圆角楼梯): {forward_bias_count}")
    print(f"后退倾向地形块数量 (普通楼梯): {backward_bias_count}")
    print(f"中性地形块数量: {neutral_count}")
    
    # 检查圆角楼梯mesh的数量
    print(f"收集到的圆角楼梯mesh数量: {len(terrain.collected_stair_meshes)}")
    
    return terrain

if __name__ == "__main__":
    try:
        terrain = test_terrain_velocity_bias()
        print("\n测试完成！地形速度倾向功能正常工作。")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
